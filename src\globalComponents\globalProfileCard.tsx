"use client";
import { Badge } from "@/components/ui/badge";
import useAuth from "@/hook";
import { FollowerManager } from "@/services/followServices";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { X } from "react-feather";
import AuthSignup from "@/screens/auth";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import { getUserId } from "@/utils/userUtils";
import { generateFileUrl } from "@/lib/utils";

const GlobalProfileCard = (props: any) => {
  // Get authenticated user (must be at the top level)
  const user = useAuth();

  const [isSigninOpen, setIsSigninOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Use the isFollow prop directly instead of managing local state
  const isFollowing = props.isFollow;

  // Debug logging
  console.log(
    `GlobalProfileCard - Profile: ${props.profile_name}, isFollowing: ${isFollowing}, isLoading: ${isLoading}`
  );

  // // Generate public URL for Firebase Storage
  // const generateFileUrl = (postFile?: string) => {
  //   const baseUrl = process.env.BASE_STORAGE_URL;

  //   if (!postFile || !baseUrl) return undefined;
  //   if (postFile.startsWith("https://firebasestorage.googleapis.com/")) return postFile;

  //   return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  // };

  // Handle follow action
  const handleFollow = useCallback(
    async (id: string) => {
      console.log(`Starting follow action for ${id}`);
      setIsLoading(true);
      try {
        const userId = getUserId(user?.userData);
        if (!userId) {
          console.error("User ID is required for follow action");
          setIsLoading(false);
          return;
        }

        const resp = await FollowerManager.getInstance().FollowByUserId({
          src_id: userId,
          dest_id: id,
        });
        console.log(`Follow response:`, resp);
        if (resp == "success") {
          // Notify parent component about the follow action if callback exists
          if (props.onFollowChange) {
            console.log(`Calling onFollowChange(${id}, false)`);
            props.onFollowChange(id, false); // false means now following (button should show unfollow)
          }
          isFollowing(false);
          setIsLoading(false);
        } else {
          console.error("Follow action failed:", resp);
        }
      } catch (error) {
        console.error("Follow failed:", error);
      } finally {
        console.log(`Follow action completed, setting loading to false`);
        setIsLoading(false);
        setIsLoading(false);
      }
    },
    [user, props]
  );

  // Handle unfollow action
  const handleUnFollow = useCallback(
    async (id: string) => {
      console.log(`Starting unfollow action for ${id}`);
      setIsLoading(true);
      try {
        const userId = getUserId(user?.userData);
        if (!userId) {
          console.error("User ID is required for unfollow action");
          setIsLoading(false);
          return;
        }

        const resp = await FollowerManager.getInstance().UnfollowByUserId({
          src_id: userId,
          dest_id: id,
        });
        console.log(`Unfollow response:`, resp);
        if (resp == "success") {
          // Notify parent component about the unfollow action if callback exists
          if (props.onFollowChange) {
            console.log(`Calling onFollowChange(${id}, true)`);
            props.onFollowChange(id, true); // true means not following (button should show follow)
          }
          isFollowing(true);
          setIsLoading(false);
        } else {
          console.error("Unfollow action failed:", resp);
        }
      } catch (error) {
        console.error("Unfollow failed:", error);
      } finally {
        console.log(`Unfollow action completed, setting loading to false`);
        setIsLoading(false);
      }
    },
    [user, props]
  );

  return (
    <>
      <div className="row justify-between mb-3">
        <div className="row gap-2">
          <div>
            <img
              src={props.avatar ? generateFileUrl(props.avatar) : "/assets/profileAvatar.svg"}
              alt=""
              className="w-[40px] h-[40px] min-h-[40px] min-w-[40px] rounded-full object-cover"
              style={{
                border: "3px solid",
                borderColor: props?.themeProperties?.backgroundColor || "#000",
              }}
            />
          </div>
          <Link href={`/profile/amuzn/${props.profile_name?.replace(/\s+/g, "-")}`}>
            <div className="cursor-pointer">
              <p className="font-bold font-sf">{props.profile_name || "Profile Name*"}</p>
              <p className="text-[#616770] line-clamp-1 -mt-2">{props.location || "Location*"}</p>
            </div>
          </Link>
        </div>
        {user.userId !== props?.id && (
          <div>
            {user.isLogin ? (
              <div>
                {isFollowing ? (
                  <Badge
                    className={`btn-xs font-normal font-sf text-white min-w-20 w-20 ${
                      isLoading ? "pointer-events-none opacity-70" : ""
                    }`}
                    onClick={() => !isLoading && handleFollow(props?.id)}
                    style={{ fontWeight: 400 }}
                  >
                    {isLoading ? (
                      <span className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                    ) : (
                      "Follow"
                    )}
                  </Badge>
                ) : (
                  <Badge
                    className={`btn-xs font-normal font-sf border-primary btn min-w-20 w-20 ${
                      isLoading ? "pointer-events-none opacity-70" : ""
                    }`}
                    variant="outline"
                    onClick={() => !isLoading && handleUnFollow(props?.id)}
                    style={{ fontWeight: 400 }}
                  >
                    {isLoading ? (
                      <span className="w-4 h-4 border-2 border-gray-500 border-t-transparent rounded-full animate-spin"></span>
                    ) : (
                      "Unfollow"
                    )}
                  </Badge>
                )}
              </div>
            ) : (
              <Badge
                className="btn-xs font-normal font-sf text-white min-w-20 w-20"
                onClick={() => setIsSigninOpen(true)}
                style={{ fontWeight: 400 }}
              >
                Follow
              </Badge>
            )}
          </div>
        )}
      </div>
      <div className="max-md:h-full px-5">
        <AlertDialog
          open={isSigninOpen}
          // onOpenChange={(val: any) => {
          //   console.log(val, "valvalvalvalval");
          // }}
        >
          <AlertDialogTrigger asChild></AlertDialogTrigger>
          <AlertDialogContent className="py-10 px-28 max-md:px-8 md:rounded-xl  max-md:h-full max-md:w-full max-md:-mt-[1px] max-md:overflow-scroll">
            <AlertDialogHeader>
              <AlertDialogDescription className=" max-md: overflow-scroll h-full hide-scroll ">
                <div
                  className="absolute top-6 left-6 cursor-pointer"
                  onClick={() => {
                    sessionStorage.removeItem("input");
                    sessionStorage.removeItem("openPost");
                    setIsSigninOpen(false);
                  }}
                >
                  <X />
                </div>
                <AuthSignup onClose={() => setIsSigninOpen(false)} />
              </AlertDialogDescription>
            </AlertDialogHeader>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
};

export default GlobalProfileCard;
